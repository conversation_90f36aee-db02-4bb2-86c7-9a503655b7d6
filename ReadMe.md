# 📊 爬虫数据展示平台技术规划（v1.0）

---

## 2. 技术选型 & 版本锁定

| 层级     | 技术                      | 版本            | 说明                          |
| -------- | ------------------------- | --------------- | ----------------------------- |
| 采集     | 青龙面板                  | ≥ v2.15.x       | 已支持 sqlite3 输出           |
| 解析服务 | FastAPI + SQLAlchemy      | 0.110.x / 2.0.x | 异步高并发文件监控            |
| 管理后台 | Vue3 + TypeScript + Antdv | 3.x / 4.x       | 动态表单、拖拽排序            |
| 展示前端 | 同仓库复用                | -               | 响应式 PC & Mobile            |
| 缓存     | Redis                     | 7.x             | 元数据缓存 & 查询结果缓存 60s |
| 部署     | docker-compose            | latest          | 一键 `up -d`                  |

---

## 3. 文件目录规范（强制）

```
/data/
├── spider/                 # 青龙映射目录
│   ├── job_a.db          # 任务A结果
│   ├── job_b.db          # 任务B结果
│   └── ...               # 任意数量
└── meta/                 # 系统自用
    └── admin.db          # 侧栏+页面配置库
```

> 约定：
>
> - 每个爬虫任务将仅写入 **一个 sqlite 文件**
> - 爬虫任务不交由这个系统来管理，我将使用青龙面板来统一管理
> - 字段名首字母小写驼峰（如 `price`, `updateTime`）

---

## 4. 数据解析服务（FastAPI）

### 4.1 核心接口

| Method   | Path                  | 描述                                                   |
| -------- | --------------------- | ------------------------------------------------------ |
| `GET`    | `/api/discover`       | 扫描 `/data/spider` 下所有 `.db`，返回库 → 表 → 字段树 |
| `POST`   | `/api/page`           | 新建/修改侧栏页（绑定库+表+字段+排序+分页）            |
| `GET`    | `/api/page/{id}/data` | 根据配置动态查询并返回分页 JSON                        |
| `DELETE` | `/api/page/{id}`      | 删除侧栏页                                             |

### 4.2 查询 DSL（管理员在 UI 里点选，系统生成）

```json5
{
  db: "job_a.db",
  table: "jd_price",
  select: ["sku", "price", "stock"],
  where: {
    price: { op: ">", value: 100 },
  },
  order: { field: "updateTime", direction: "desc" },
  page: 1,
  size: 20,
}
```

---

## 5. 管理后台功能列表（Vue3）

### 5.1 侧栏页面 CRUD

- **新增页面**

  - Step1：选择 `.db` → 自动列出所有表 → 选择表
  - Step2：勾选可展示字段（可多选）
  - Step3：设置筛选字段（单选/多选/日期/数值范围）
  - Step4：设置排序字段 + 分页大小
  - Step5：配置 Banner（开/关 + 颜色 + 文案）
  - Step6：保存 → 侧栏立即出现入口

- **拖拽排序**

  - 侧栏顺序可上下拖动实时生效

### 5.2 数据预览

- **实时预览**

  - 在后台即可分页查看最终效果，无需切换用户端

- **字段映射**
  - 支持「数据库字段 → 展示名称」映射（如 `price` → `当前价格`）

---

## 6. 展示前端（用户可见）

### 6.1 自适应布局

| 设备   | 布局                          |
| ------ | ----------------------------- |
| PC     | 左侧固定侧栏 200px + 右侧内容 |
| Mobile | 顶部汉堡菜单抽屉侧栏          |

### 6.2 交互细节

- 管理员可以为不同用户设置可以看到的页面，不显示其他没有权限的页面
- Banner 固定在顶部，可手动关闭
- 筛选区折叠 / 展开（记忆状态）
- 分页器：页码 + 快速跳转 + 每页条数选择（10/20/50/100）

---

## 7. 缓存与刷新策略

| 缓存位置         | TTL    | 触发刷新            |
| ---------------- | ------ | ------------------- |
| 元数据（表结构） | 5 min  | 文件 `mtime` 变化   |
| 查询结果         | 60 s   | 手动刷新按钮 / 翻页 |
| 侧栏配置         | 热更新 | 后台保存后立即推送  |

---

## 8. 部署清单

### 8.1 docker-compose.yml（精简版）

```yaml
version: "3.9"
services:
  qinglong:
    image: whyour/qinglong:latest
    volumes:
      - ./ql:/ql/data
      - ./data/spider:/ql/data/db # 爬虫落盘目录
  backend:
    build: ./backend
    volumes:
      - ./data:/data
    ports:
      - "8000:8000"
  frontend:
    build: ./frontend
    ports:
      - "80:80"
  redis:
    image: redis:7-alpine
```

### 8.2 首次启动流程

```bash
# 1. 克隆仓库
git clone https://github.com/your-org/spider-view.git
cd spider-view

# 2. 一键启动
docker-compose up -d

# 3. 访问
# 管理后台 http://localhost/admin
# 展示前端 http://localhost
```

---

## 9. 二次开发指南

### 9.1 新增解析器（如 CSV/Parquet）

只需实现 `BaseAdapter` 接口：

```python
class CsvAdapter(BaseAdapter):
    def discover(self, file_path): ...
    def query(self, config: QueryConfig): ...
```

### 9.2 前端主题色

- 全局 Less 变量：`@primary-color`, `@banner-blue`, `@banner-yellow`, `@banner-red`
- 提供暗黑模式开关（CSS 变量自动切换）

---
